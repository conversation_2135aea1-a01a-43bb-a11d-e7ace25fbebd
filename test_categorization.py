#!/usr/bin/env python3
"""
Test script to validate the job categorization functionality.
Tests the categorization logic with sample job titles.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Import the categorization function from the API
from api import categorize_job_by_title

def test_categorization():
    """Test the job categorization with sample titles."""
    
    # Test cases with expected categories
    test_cases = [
        # General Management & Executive
        ("Geschäftsführer (m/w/d)", "General Management & Executive"),
        ("CEO Technology Company", "General Management & Executive"),
        ("Niederlassungsleiter München", "General Management & Executive"),
        ("Abteilungsleitung Marketing", "General Management & Executive"),
        
        # Sales & Business Development
        ("Key Account Manager (m/w/d)", "Sales & Business Development"),
        ("Vertriebsleiter Deutschland", "Sales & Business Development"),
        ("Business Development Manager", "Sales & Business Development"),
        ("Area Sales Manager Europe", "Sales & Business Development"),
        
        # Finance & Controlling
        ("Finanzbuchhalter (m/w/d)", "Finance & Controlling"),
        ("Controller Automotive", "Finance & Controlling"),
        ("Junior Controller", "Finance & Controlling"),
        ("Director Finance", "Finance & Controlling"),
        
        # Operations & Logistics
        ("Projektleiter Bau (m/w/d)", "Operations & Logistics"),
        ("Betriebsleiter Produktion", "Operations & Logistics"),
        ("Supply Chain Manager", "Operations & Logistics"),
        ("Logistikleitung", "Operations & Logistics"),
        
        # Engineering & Technical
        ("Elektroingenieur (m/w/d)", "Engineering & Technical"),
        ("Servicetechniker", "Engineering & Technical"),
        ("Mechatroniker", "Engineering & Technical"),
        ("Software Engineer", "Engineering & Technical"),
        
        # Construction & Infrastructure
        ("Bauleiter (m/w/d)", "Construction & Infrastructure"),
        ("Kalkulator Bau", "Construction & Infrastructure"),
        ("Architekt", "Construction & Infrastructure"),
        ("Facility Manager", "Construction & Infrastructure"),
        
        # IT & Digital
        ("IT Consultant", "IT & Digital"),
        ("Cloud Architect", "IT & Digital"),
        ("DevOps Engineer", "IT & Digital"),
        ("SAP Consultant", "IT & Digital"),
        
        # HR & People Management
        ("HR Manager", "HR & People Management"),
        ("Personalberater", "HR & People Management"),
        ("Executive Search Consultant", "HR & People Management"),
        ("Recruiter", "HR & People Management"),
        
        # Legal, Compliance & Administration
        ("Payroll Specialist", "Legal, Compliance & Administration"),
        ("Compliance Officer", "Legal, Compliance & Administration"),
        ("Assistenz der Geschäftsführung", "Legal, Compliance & Administration"),
        ("Rechtsanwalt", "Legal, Compliance & Administration"),
        
        # Scientific/Specialized
        ("Geologe (m/w/d)", "Scientific/Specialized"),
        ("Laborfachkraft Metallurgie", "Scientific/Specialized"),
        ("Chemiker", "Scientific/Specialized"),
        ("Research Scientist", "Scientific/Specialized"),
        
        # Real Estate & Property
        ("Immobilienkaufmann", "Real Estate & Property"),
        ("Property Manager", "Real Estate & Property"),
        ("Objektverwalter", "Real Estate & Property"),
        ("Makler", "Real Estate & Property"),
        
        # Other/Unspecified (should not match any category)
        ("Mysterious Job Title XYZ", "Other/Unspecified"),
        ("Random Position ABC", "Other/Unspecified"),
    ]
    
    print("🧪 Testing Job Categorization Functionality")
    print("=" * 60)
    
    correct = 0
    total = len(test_cases)
    
    for job_title, expected_category in test_cases:
        actual_category = categorize_job_by_title(job_title)
        is_correct = actual_category == expected_category
        
        status = "✅" if is_correct else "❌"
        print(f"{status} '{job_title}'")
        print(f"   Expected: {expected_category}")
        print(f"   Actual:   {actual_category}")
        
        if is_correct:
            correct += 1
        else:
            print(f"   ⚠️  MISMATCH!")
        
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {correct}/{total} correct ({correct/total*100:.1f}%)")
    
    if correct == total:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"⚠️  {total - correct} tests failed")
        return False

if __name__ == "__main__":
    success = test_categorization()
    sys.exit(0 if success else 1)
