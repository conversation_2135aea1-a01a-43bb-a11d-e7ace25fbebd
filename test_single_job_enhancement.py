#!/usr/bin/env python3
"""
Test enhanced job description fetching on a single job URL.
"""

import os
import sys
from dotenv import load_dotenv

# Add backend to path
sys.path.append('backend')

from scraper import JobScraper

def test_single_job_enhancement():
    """Test enhanced description fetching on a single job."""
    load_dotenv()
    
    # Test with a specific job URL that should have detailed description
    test_job_url = "https://www.hapeko.de/bewerbende/stellenangebot/jle-127329-key-account-manager-m-w-d-account-manager-m-w-d-fuer-quereinstieg-in-die-personalberatung"
    
    print("🧪 TESTING SINGLE JOB DESCRIPTION ENHANCEMENT")
    print("=" * 60)
    print(f"Testing Job URL: {test_job_url}")
    print()
    
    # Initialize scraper
    scraper = JobScraper()
    
    def log_callback(msg_type, message, data=None):
        print(f"[{msg_type.upper()}] {message}")
    
    try:
        # Test the enhanced description fetching method directly
        job_data = {
            "title": "Key Account Manager (m/w/d)",
            "company": "Hapeko",
            "location": "Freiburg",
            "summary": "50.000–130.000 EUR Jahresgehalt"  # This is what we currently have
        }
        
        print("📋 BEFORE ENHANCEMENT:")
        print(f"   Title: {job_data['title']}")
        print(f"   Current Description: '{job_data['summary']}'")
        print(f"   Description Length: {len(job_data['summary'])} chars")
        print(f"   Quality: {'❌ SALARY ONLY' if scraper._is_salary_only_description(job_data['summary']) else '✅ GOOD'}")
        print()
        
        print("🔍 FETCHING ENHANCED DESCRIPTION...")
        enhanced_description = scraper._fetch_full_job_description(test_job_url, job_data, log_callback)
        
        print(f"\n📋 AFTER ENHANCEMENT:")
        print(f"   Enhanced Description Length: {len(enhanced_description)} chars")
        print(f"   Quality: {'❌ STILL POOR' if len(enhanced_description) < 100 else '✅ ENHANCED'}")
        print(f"   Enhanced Description:")
        print(f"   '{enhanced_description}'")
        
        # Compare before and after
        improvement_ratio = len(enhanced_description) / len(job_data['summary'])
        print(f"\n📈 ENHANCEMENT RESULTS:")
        print(f"   Original length: {len(job_data['summary'])} chars")
        print(f"   Enhanced length: {len(enhanced_description)} chars")
        print(f"   Improvement ratio: {improvement_ratio:.1f}x")
        
        if improvement_ratio > 3:
            print("   ✅ SUCCESS: Significant enhancement achieved!")
        elif improvement_ratio > 1.5:
            print("   ⚠️ PARTIAL: Some improvement, but could be better")
        else:
            print("   ❌ FAILED: No significant enhancement")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

def test_salary_detection():
    """Test the salary-only description detection."""
    print("\n" + "=" * 60)
    print("🔍 TESTING SALARY-ONLY DETECTION")
    print("=" * 60)
    
    scraper = JobScraper()
    
    test_descriptions = [
        "50.000–130.000 EUR Jahresgehalt",
        "120.000–140.000 EUR Jahresgehalt",
        "Not specified",
        "Ein Controller (m/w/d) mit Schwerpunkt Produktion ist bei unserem Klienten noch zu besetzen.",
        "Für ein aufstrebendes, inhabergeführtes Unternehmen in der Lebensmittel- und Genussmittelindustrie suchen wir eine engagierte, dynamische kaufmännische Geschäftsführung.",
        "€45,000 - €60,000 per year",
        "Competitive salary package with benefits and bonus structure for experienced professionals"
    ]
    
    print("📋 Testing salary detection on various descriptions:")
    for i, desc in enumerate(test_descriptions, 1):
        is_salary_only = scraper._is_salary_only_description(desc)
        status = "❌ SALARY ONLY" if is_salary_only else "✅ GOOD"
        print(f"   {i}. {status}: '{desc[:50]}{'...' if len(desc) > 50 else ''}'")
    
    print(f"\n💡 Salary-only descriptions will be enhanced during scraping")

if __name__ == "__main__":
    test_salary_detection()
    
    print(f"\n💡 To test single job enhancement (uses API calls), uncomment:")
    print(f"   # test_single_job_enhancement()")
    
    # Uncomment to test actual enhancement (uses Firecrawl API):
    # test_single_job_enhancement()
