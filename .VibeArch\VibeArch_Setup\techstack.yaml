﻿# Techstack Configuration
# This file contains the manually configured techstack information

languages:
  - JavaScript
  - TypeScript
  - HTML
  - CSS

frontend_frameworks:
  - React
  - Vite

backend_frameworks:
  - Node.js
  - Express

build_tools:
  - npm
  - Vite

automation_tools: []

databases: []

other_technologies:
  - ESLint
  - Prettier

project_structure:
  frontend_dirs:
    - frontend-vite
  backend_dirs:
    - backend
  config_dirs:
    - .VibeArch

custom_notes: |
  This is a custom configuration for the JoMaDe_Dev project.
  The techstack has been manually configured to exclude C/C++ files
  that might be found in node_modules or other dependencies.
