#!/usr/bin/env python3
"""
Test that source display names are now showing domain names instead of generic names.
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_shortlisted_jobs_source_display():
    """Test that shortlisted jobs show domain names in source_display."""
    print("🧪 TESTING SHORTLISTED JOBS SOURCE DISPLAY")
    print("=" * 50)
    
    try:
        # Get shortlisted jobs
        response = requests.get(f"{API_BASE}/api/jobs/shortlist")
        if response.status_code != 200:
            print(f"❌ Failed to get shortlisted jobs: {response.status_code}")
            return
        
        data = response.json()
        jobs = data.get('jobs', [])
        
        if not jobs:
            print("⚠️ No shortlisted jobs found - run shortlisting first")
            return
        
        print(f"📊 Found {len(jobs)} shortlisted jobs")
        print("\n📋 Source Display Analysis:")
        
        domain_names = 0
        generic_names = 0
        
        for i, job in enumerate(jobs[:10], 1):  # Show first 10
            job_id = job.get('id', 'Unknown')
            title = job.get('title', 'Unknown Title')
            source = job.get('source', 'Unknown')
            source_display = job.get('source_display', 'Unknown')
            
            # Check if source_display is a domain name or generic
            if source_display.startswith('Job Source ') or source_display == source:
                status = "❌ GENERIC"
                generic_names += 1
            else:
                status = "✅ DOMAIN"
                domain_names += 1
            
            print(f"   {i:2d}. {job_id}: {title[:40]}...")
            print(f"       Source: {source} → Display: '{source_display}' ({status})")
        
        print(f"\n📈 RESULTS:")
        print(f"   Domain names: {domain_names}")
        print(f"   Generic names: {generic_names}")
        
        if generic_names == 0:
            print("   ✅ SUCCESS: All jobs show domain names!")
        else:
            print("   ❌ Some jobs still show generic names")
            
    except Exception as e:
        print(f"❌ Error testing shortlisted jobs: {e}")

def test_comprehensive_analysis_source_display():
    """Test that comprehensive analysis shows domain names in source_display."""
    print("\n" + "=" * 50)
    print("🧪 TESTING COMPREHENSIVE ANALYSIS SOURCE DISPLAY")
    print("=" * 50)
    
    try:
        # Get comprehensive analysis results
        response = requests.post(f"{API_BASE}/api/jobs/comprehensive-match?temp=0.7")
        if response.status_code != 200:
            print(f"❌ Failed to get comprehensive analysis: {response.status_code}")
            return
        
        data = response.json()
        jobs = data.get('jobs', [])
        
        if not jobs:
            print("⚠️ No comprehensive analysis results found - run comprehensive analysis first")
            return
        
        print(f"📊 Found {len(jobs)} comprehensive analysis results")
        print("\n📋 Source Display Analysis:")
        
        domain_names = 0
        generic_names = 0
        
        for i, job in enumerate(jobs[:10], 1):  # Show first 10
            job_id = job.get('id', 'Unknown')
            title = job.get('title', 'Unknown Title')
            source = job.get('source', 'Unknown')
            source_display = job.get('source_display', 'Unknown')
            stage2_confidence = job.get('stage2_confidence', 0)
            
            # Check if source_display is a domain name or generic
            if source_display.startswith('Job Source ') or source_display == source:
                status = "❌ GENERIC"
                generic_names += 1
            else:
                status = "✅ DOMAIN"
                domain_names += 1
            
            print(f"   {i:2d}. {job_id}: {title[:40]}...")
            print(f"       Source: {source} → Display: '{source_display}' ({status})")
            print(f"       Stage 2 Confidence: {stage2_confidence}%")
        
        print(f"\n📈 RESULTS:")
        print(f"   Domain names: {domain_names}")
        print(f"   Generic names: {generic_names}")
        
        if generic_names == 0:
            print("   ✅ SUCCESS: All jobs show domain names!")
        else:
            print("   ❌ Some jobs still show generic names")
            
    except Exception as e:
        print(f"❌ Error testing comprehensive analysis: {e}")

def test_job_urls_data():
    """Test that job URLs data has been updated with domain names."""
    print("\n" + "=" * 50)
    print("🧪 TESTING JOB URLS DATA")
    print("=" * 50)
    
    try:
        # Get job URLs
        response = requests.get(f"{API_BASE}/api/urls")
        if response.status_code != 200:
            print(f"❌ Failed to get job URLs: {response.status_code}")
            return
        
        urls = response.json()
        
        if not urls:
            print("⚠️ No job URLs found")
            return
        
        print(f"📊 Found {len(urls)} job URLs")
        print("\n📋 URL Name Analysis:")
        
        domain_names = 0
        generic_names = 0
        
        for i, url_data in enumerate(urls[:10], 1):  # Show first 10
            prefix = url_data.get('prefix', 'Unknown')
            name = url_data.get('name', 'Unknown')
            url = url_data.get('url', 'Unknown')
            
            # Check if name is a domain name or generic
            if name.startswith('Job Source ') or name == prefix:
                status = "❌ GENERIC"
                generic_names += 1
            else:
                status = "✅ DOMAIN"
                domain_names += 1
            
            print(f"   {i:2d}. {prefix}: '{name}' ({status})")
            print(f"       URL: {url[:60]}{'...' if len(url) > 60 else ''}")
        
        print(f"\n📈 RESULTS:")
        print(f"   Domain names: {domain_names}")
        print(f"   Generic names: {generic_names}")
        
        if generic_names == 0:
            print("   ✅ SUCCESS: All URLs have domain names!")
        else:
            print("   ❌ Some URLs still have generic names")
            
    except Exception as e:
        print(f"❌ Error testing job URLs: {e}")

def main():
    """Run all source display tests."""
    print("🧪 SOURCE DISPLAY FIX VERIFICATION")
    print("=" * 60)
    
    # Check if backend is running
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend not responding - please start the server first")
            return
    except Exception:
        print("❌ Cannot connect to backend - please start the server first")
        return
    
    print("✅ Backend is running")
    
    # Run tests
    test_job_urls_data()
    test_shortlisted_jobs_source_display()
    test_comprehensive_analysis_source_display()
    
    print("\n" + "=" * 60)
    print("🎉 SOURCE DISPLAY VERIFICATION COMPLETE!")
    print("\n💡 Expected Results:")
    print("   - Job URLs should show domain names like 'hapeko', 'psp', etc.")
    print("   - Shortlisted jobs should display 'Source: hapeko' instead of 'Source: Job Source AAA'")
    print("   - Comprehensive analysis should show clean domain names")
    print("   - UI should display readable source names in job listings")

if __name__ == "__main__":
    main()
