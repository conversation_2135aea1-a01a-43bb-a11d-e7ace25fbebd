#!/usr/bin/env python3
"""
Test the improved URL extraction logic on the problematic URL.
"""

import os
import sys
from dotenv import load_dotenv

# Add backend to path
sys.path.append('backend')

from scraper import JobScraper

def test_problematic_url():
    """Test the problematic URL that was creating duplicate jobs."""
    load_dotenv()
    
    # The problematic URL
    test_url = "https://www.meyheadhunter.de/mandate/mechatroniker-kaeltetechnik-mwd-im-service-in-ihrer-region/"
    
    print("🧪 TESTING URL EXTRACTION FIX")
    print("=" * 50)
    print(f"Testing URL: {test_url}")
    print()
    
    # Initialize scraper
    scraper = JobScraper()
    
    def log_callback(msg_type, message, data=None):
        print(f"[{msg_type.upper()}] {message}")
    
    try:
        # Test the crawl
        jobs = scraper.crawl_url(test_url, "TEST", log_callback, force_scrape=True)
        
        print(f"\n📊 RESULTS:")
        print(f"   Jobs extracted: {len(jobs)}")
        print()
        
        if jobs:
            print("📋 JOB DETAILS:")
            for i, job in enumerate(jobs, 1):
                print(f"   Job {i}:")
                print(f"      ID: {job.get('id')}")
                print(f"      Title: {job.get('title')}")
                print(f"      Location: {job.get('location')}")
                print(f"      URL: {job.get('link')}")
                print(f"      Description: {job.get('description', '')[:100]}...")
                print()
            
            # Check for URL duplicates
            urls = [job.get('link') for job in jobs]
            unique_urls = set(urls)
            
            print(f"🔗 URL ANALYSIS:")
            print(f"   Total jobs: {len(jobs)}")
            print(f"   Unique URLs: {len(unique_urls)}")
            
            if len(urls) != len(unique_urls):
                print("   ❌ DUPLICATE URLs FOUND!")
                from collections import Counter
                url_counts = Counter(urls)
                for url, count in url_counts.items():
                    if count > 1:
                        print(f"      - {url}: {count} jobs")
            else:
                print("   ✅ All URLs are unique")
        else:
            print("   ❌ No jobs extracted")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

def test_multiple_urls():
    """Test multiple URLs to ensure the fix works broadly."""
    test_urls = [
        ("https://www.meyheadhunter.de/mandate/mechatroniker-kaeltetechnik-mwd-im-service-in-ihrer-region/", "ABN"),
        ("https://www.psp.de/stellenausschreibungen.html?stelle=1062", "AAD"),
        ("https://www.psp.de/stellenausschreibungen.html?stelle=1061", "AAD"),
    ]
    
    print("\n" + "=" * 50)
    print("🧪 TESTING MULTIPLE PROBLEMATIC URLs")
    print("=" * 50)
    
    scraper = JobScraper()
    
    def log_callback(msg_type, message, data=None):
        if msg_type in ['error', 'warning']:
            print(f"[{msg_type.upper()}] {message}")
    
    total_jobs = 0
    total_unique_urls = 0
    
    for url, prefix in test_urls:
        print(f"\n🔍 Testing: {url}")
        try:
            jobs = scraper.crawl_url(url, prefix, log_callback, force_scrape=True)
            urls = [job.get('link') for job in jobs]
            unique_urls = len(set(urls))
            
            print(f"   Jobs: {len(jobs)}, Unique URLs: {unique_urls}")
            
            if len(jobs) != unique_urls:
                print(f"   ⚠️ URL duplicates detected!")
            else:
                print(f"   ✅ All URLs unique")
                
            total_jobs += len(jobs)
            total_unique_urls += unique_urls
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Total jobs extracted: {total_jobs}")
    print(f"   Total unique URLs: {total_unique_urls}")
    
    if total_jobs == total_unique_urls:
        print("   ✅ SUCCESS: All jobs have unique URLs!")
    else:
        print("   ❌ ISSUE: Some jobs still have duplicate URLs")

if __name__ == "__main__":
    test_problematic_url()
    test_multiple_urls()
