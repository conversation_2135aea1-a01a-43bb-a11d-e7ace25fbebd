#!/usr/bin/env python3
"""
Test the shortlist caching logic to verify it works correctly.
"""

import os
import sys
sys.path.append(os.path.dirname(__file__))

from storage import JobStore, CVStore

def test_cache():
    """Test the shortlist caching logic."""
    
    # Initialize stores
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    jobs_store = JobStore(os.path.join(data_dir, "jobs.json"))
    cv_store = CVStore(os.path.join(data_dir, "cv.json"))
    
    print(f"Loaded {len(jobs_store.data)} jobs")
    print(f"Loaded {len(cv_store.data)} CV entries")
    
    # Get CV data
    cv_data = cv_store.get_all()
    if not cv_data:
        print("No CV data found!")
        return
    
    cv_info = cv_data[0]
    cv_summary = cv_info.get('summary', '')
    
    print(f"CV summary length: {len(cv_summary)} characters")
    
    # Test cache check
    current_job_count = len(jobs_store.get_scraped_jobs())
    print(f"Current scraped job count: {current_job_count}")
    
    # Test the cache logic
    cached_results = jobs_store._get_cached_shortlist(cv_summary, 0.7, current_job_count)
    
    if cached_results is not None:
        print(f"✅ Cache HIT: Found {len(cached_results)} cached results")
        print("Cache is working correctly!")
    else:
        print("❌ Cache MISS: No valid cache found")
        print("This means cache validation failed or no cache exists")
    
    # Test shortlisting with cache
    print("\n--- Testing shortlisting with cache ---")
    shortlisted = jobs_store.shortlist_jobs_by_cv(cv_info, temp=0.7, min_confidence=75.0, force_refresh=False)
    print(f"Shortlisted jobs: {len(shortlisted)}")

if __name__ == "__main__":
    test_cache()
