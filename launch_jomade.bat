@echo off
title Jomade MVP - Executive Search Application

echo Starting Jomade MVP...
echo.

cd /d "%~dp0"

if not exist "backend\app.py" (
    echo ERROR: backend\app.py not found
    echo Please run this from the Jomade MVP directory
    pause
    exit
)

echo Installing dependencies...
pip install -r requirements.txt --quiet

echo.
echo Starting server...
echo The application will open at: http://localhost:3000
echo.
echo Press Ctrl+C to stop the application
echo.

python backend\app.py

echo.
echo Application stopped.
pause
