#!/usr/bin/env python3
"""
Analyze job URL data integrity and identify inconsistencies.
Check for duplicate URLs, incorrect URL assignments, and data quality issues.
"""

import json
from collections import defaultdict, Counter
from urllib.parse import urlparse
import re

def load_jobs_data():
    """Load jobs data from JSON file."""
    try:
        with open('data/jobs.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ jobs.json file not found!")
        return []

def analyze_url_integrity(jobs):
    """Analyze URL integrity and identify issues."""
    print("🔍 ANALYZING JOB URL DATA INTEGRITY")
    print("=" * 50)
    
    # Track URL usage
    url_to_jobs = defaultdict(list)
    source_to_urls = defaultdict(set)
    job_id_pattern = re.compile(r'^([A-Z]{3})(\d{2})(\d{2})$')
    
    # Collect data
    for job in jobs:
        job_id = job.get('id', '')
        job_url = job.get('link', '')
        source = job.get('source', '')
        title = job.get('title', '')
        
        if job_url:
            url_to_jobs[job_url].append({
                'id': job_id,
                'title': title,
                'source': source
            })
            source_to_urls[source].add(job_url)
    
    print(f"📊 SUMMARY:")
    print(f"   Total jobs: {len(jobs)}")
    print(f"   Unique URLs: {len(url_to_jobs)}")
    print(f"   Sources: {len(source_to_urls)}")
    print()
    
    # 1. Find duplicate URLs (same URL used by multiple jobs)
    print("🚨 DUPLICATE URL ANALYSIS:")
    duplicate_urls = {url: job_list for url, job_list in url_to_jobs.items() if len(job_list) > 1}
    
    if duplicate_urls:
        print(f"   Found {len(duplicate_urls)} URLs used by multiple jobs:")
        for url, job_list in list(duplicate_urls.items())[:10]:  # Show first 10
            print(f"   📎 {url}")
            for job in job_list:
                print(f"      - {job['id']}: {job['title'][:60]}...")
            print()
    else:
        print("   ✅ No duplicate URLs found")
    
    # 2. Analyze URL patterns by source
    print("🔗 URL PATTERNS BY SOURCE:")
    for source in sorted(source_to_urls.keys()):
        urls = source_to_urls[source]
        print(f"   {source}: {len(urls)} unique URLs")
        
        # Sample URLs for each source
        sample_urls = list(urls)[:3]
        for url in sample_urls:
            domain = urlparse(url).netloc
            print(f"      - {domain}")
        if len(urls) > 3:
            print(f"      ... and {len(urls) - 3} more")
        print()
    
    # 3. Check for jobs with same URL but different sources
    print("⚠️  CROSS-SOURCE URL CONFLICTS:")
    cross_source_conflicts = []
    for url, job_list in url_to_jobs.items():
        sources = set(job['source'] for job in job_list)
        if len(sources) > 1:
            cross_source_conflicts.append((url, job_list))
    
    if cross_source_conflicts:
        print(f"   Found {len(cross_source_conflicts)} URLs used across different sources:")
        for url, job_list in cross_source_conflicts[:5]:  # Show first 5
            sources = set(job['source'] for job in job_list)
            print(f"   📎 {url}")
            print(f"      Sources: {', '.join(sources)}")
            for job in job_list:
                print(f"      - {job['source']}/{job['id']}: {job['title'][:50]}...")
            print()
    else:
        print("   ✅ No cross-source URL conflicts found")
    
    # 4. Check job ID consistency
    print("🔢 JOB ID CONSISTENCY:")
    id_issues = []
    for job in jobs:
        job_id = job.get('id', '')
        source = job.get('source', '')
        
        match = job_id_pattern.match(job_id)
        if match:
            id_source = match.group(1)
            if id_source != source:
                id_issues.append({
                    'id': job_id,
                    'expected_source': id_source,
                    'actual_source': source,
                    'title': job.get('title', '')
                })
    
    if id_issues:
        print(f"   Found {len(id_issues)} jobs with ID/source mismatches:")
        for issue in id_issues[:10]:  # Show first 10
            print(f"   ❌ {issue['id']}: expected source '{issue['expected_source']}', got '{issue['actual_source']}'")
            print(f"      Title: {issue['title'][:60]}...")
    else:
        print("   ✅ All job IDs match their sources")
    
    # 5. Most problematic URLs
    print("\n🎯 MOST PROBLEMATIC URLS:")
    most_duplicated = sorted(duplicate_urls.items(), key=lambda x: len(x[1]), reverse=True)[:5]
    
    for url, job_list in most_duplicated:
        print(f"   📎 {url}")
        print(f"      Used by {len(job_list)} jobs:")
        for job in job_list:
            print(f"      - {job['source']}/{job['id']}: {job['title'][:50]}...")
        print()
    
    return {
        'total_jobs': len(jobs),
        'unique_urls': len(url_to_jobs),
        'duplicate_urls': len(duplicate_urls),
        'cross_source_conflicts': len(cross_source_conflicts),
        'id_issues': len(id_issues)
    }

def main():
    """Main analysis function."""
    jobs = load_jobs_data()
    if not jobs:
        return
    
    stats = analyze_url_integrity(jobs)
    
    print("\n" + "=" * 50)
    print("📋 FINAL ASSESSMENT:")
    print(f"   Total jobs analyzed: {stats['total_jobs']}")
    print(f"   Unique URLs: {stats['unique_urls']}")
    print(f"   Duplicate URL issues: {stats['duplicate_urls']}")
    print(f"   Cross-source conflicts: {stats['cross_source_conflicts']}")
    print(f"   Job ID issues: {stats['id_issues']}")
    
    if stats['duplicate_urls'] > 0 or stats['cross_source_conflicts'] > 0:
        print("\n❌ DATA INTEGRITY ISSUES FOUND!")
        print("   URLs cannot be reliably used to fetch full job descriptions")
        print("   until these issues are resolved.")
    else:
        print("\n✅ URL DATA INTEGRITY LOOKS GOOD!")
        print("   URLs can be used to fetch full job descriptions.")

if __name__ == "__main__":
    main()
