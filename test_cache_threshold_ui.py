#!/usr/bin/env python3
"""
Test the UI cache threshold functionality.
"""

import requests
import json
import os
from datetime import datetime, timedelta

API_BASE = "http://localhost:8000"

def test_cache_settings_api():
    """Test the cache settings API endpoints."""
    print("🧪 TESTING CACHE SETTINGS API")
    print("=" * 50)
    
    # Test getting default settings
    print("1. Testing GET cache settings...")
    try:
        response = requests.get(f"{API_BASE}/api/scrape/cache/settings")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Default settings: {data.get('settings', {})}")
        else:
            print(f"   ❌ Failed to get settings: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test updating settings
    print("\n2. Testing PUT cache settings (48 hours)...")
    try:
        response = requests.put(
            f"{API_BASE}/api/scrape/cache/settings",
            json={"cache_hours": 48}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Updated settings: {data.get('message', 'Success')}")
        else:
            print(f"   ❌ Failed to update settings: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test getting updated settings
    print("\n3. Testing GET updated settings...")
    try:
        response = requests.get(f"{API_BASE}/api/scrape/cache/settings")
        if response.status_code == 200:
            data = response.json()
            settings = data.get('settings', {})
            cache_hours = settings.get('cache_hours', 24)
            print(f"   ✅ Current settings: {cache_hours} hours")
            
            if cache_hours == 48:
                print("   ✅ Settings update successful!")
            else:
                print(f"   ❌ Settings not updated correctly (expected 48, got {cache_hours})")
        else:
            print(f"   ❌ Failed to get updated settings: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test cache status with new settings
    print("\n4. Testing cache status with new threshold...")
    try:
        response = requests.get(f"{API_BASE}/api/scrape/cache/status")
        if response.status_code == 200:
            data = response.json()
            cache_status = data.get('cache_status', {})
            print(f"   ✅ Cache status with {cache_status.get('cache_hours', 24)}h threshold:")
            print(f"      Total URLs: {cache_status.get('total_cached_urls', 0)}")
            print(f"      Fresh cache: {cache_status.get('fresh_cache_count', 0)}")
            print(f"      Expired cache: {cache_status.get('expired_cache_count', 0)}")
        else:
            print(f"   ❌ Failed to get cache status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test invalid settings
    print("\n5. Testing invalid settings (200 hours - should fail)...")
    try:
        response = requests.put(
            f"{API_BASE}/api/scrape/cache/settings",
            json={"cache_hours": 200}
        )
        if response.status_code == 400:
            print("   ✅ Correctly rejected invalid setting")
        else:
            print(f"   ❌ Should have rejected invalid setting: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Reset to default
    print("\n6. Resetting to default (24 hours)...")
    try:
        response = requests.put(
            f"{API_BASE}/api/scrape/cache/settings",
            json={"cache_hours": 24}
        )
        if response.status_code == 200:
            print("   ✅ Reset to default settings")
        else:
            print(f"   ❌ Failed to reset: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_cache_file_creation():
    """Test that cache settings file is created correctly."""
    print("\n" + "=" * 50)
    print("🔍 TESTING CACHE SETTINGS FILE")
    print("=" * 50)
    
    settings_file = "data/cache_settings.json"
    
    # Check if settings file exists
    if os.path.exists(settings_file):
        print(f"✅ Settings file exists: {settings_file}")
        
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)
            print(f"✅ Settings content: {settings}")
            
            cache_hours = settings.get('cache_hours', 24)
            if 1 <= cache_hours <= 168:
                print(f"✅ Valid cache_hours value: {cache_hours}")
            else:
                print(f"❌ Invalid cache_hours value: {cache_hours}")
                
        except Exception as e:
            print(f"❌ Error reading settings file: {e}")
    else:
        print(f"⚠️ Settings file not found: {settings_file}")
        print("   This is normal if no settings have been saved yet")

def test_cache_threshold_impact():
    """Test how different thresholds affect cache status."""
    print("\n" + "=" * 50)
    print("🕐 TESTING CACHE THRESHOLD IMPACT")
    print("=" * 50)
    
    # Load current cache data
    cache_file = "data/firecrawl_cache.json"
    if not os.path.exists(cache_file):
        print("⚠️ No cache file found - cannot test threshold impact")
        return
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        print(f"📊 Found {len(cache_data)} cached URLs")
        
        # Test different thresholds
        thresholds = [1, 6, 12, 24, 48, 72]
        
        for threshold in thresholds:
            fresh_count = 0
            expired_count = 0
            
            for url, cache_info in cache_data.items():
                try:
                    cached_time = datetime.fromisoformat(cache_info['timestamp'])
                    if datetime.now() - cached_time < timedelta(hours=threshold):
                        fresh_count += 1
                    else:
                        expired_count += 1
                except Exception:
                    expired_count += 1
            
            print(f"   {threshold:2d}h threshold: {fresh_count:2d} fresh, {expired_count:2d} expired")
            
    except Exception as e:
        print(f"❌ Error analyzing cache data: {e}")

def main():
    """Run all cache threshold tests."""
    print("🧪 CACHE THRESHOLD UI FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Check if backend is running
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend not responding - please start the server first")
            return
    except Exception:
        print("❌ Cannot connect to backend - please start the server first")
        print("   Run: cd backend && python -m uvicorn api:app --reload --port 8000")
        return
    
    print("✅ Backend is running")
    
    # Run tests
    test_cache_settings_api()
    test_cache_file_creation()
    test_cache_threshold_impact()
    
    print("\n" + "=" * 60)
    print("🎉 CACHE THRESHOLD TESTING COMPLETE!")
    print("\n💡 To test the UI:")
    print("   1. Open http://localhost:3000/settings.html")
    print("   2. Scroll to 'Firecrawl Cache Management'")
    print("   3. Change the 'Cache Threshold (hours)' value")
    print("   4. Click 'Save Cache Settings'")
    print("   5. Check that the cache status updates with new threshold")

if __name__ == "__main__":
    main()
