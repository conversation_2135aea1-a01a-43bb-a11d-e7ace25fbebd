#!/usr/bin/env python3
"""
Test the fixed comprehensive analysis loading with correct parameters.
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_fixed_parameters():
    """Test with the corrected parameters that the frontend should now use."""
    print("🧪 Testing fixed comprehensive analysis loading...")
    
    # These are the parameters the frontend should now use
    temp = 0.7  # From min-score element
    min_confidence = 0.0  # Hardcoded to show all results
    
    try:
        url = f"{API_BASE}/api/jobs/comprehensive-analysis?temp={temp}&min_confidence={min_confidence}"
        print(f"📡 Testing URL: {url}")
        
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            analyzed_jobs = data.get('analyzed_jobs', [])
            
            print(f"✅ Success: {len(analyzed_jobs)} comprehensive analysis results")
            
            if analyzed_jobs:
                # Show some sample data
                print(f"\n📊 Sample results:")
                for i, job in enumerate(analyzed_jobs[:3]):  # Show first 3
                    title = job.get('title', 'No title')[:50] + ('...' if len(job.get('title', '')) > 50 else '')
                    stage1 = job.get('stage1_confidence', 0)
                    stage2 = job.get('stage2_confidence', 0)
                    change = job.get('confidence_change', 0)
                    print(f"   {i+1}. {title}")
                    print(f"      Stage 1: {stage1}% → Stage 2: {stage2}% (Δ{change:+.1f}%)")
                
                print(f"\n🎯 Expected frontend behavior:")
                print(f"   - Should load {len(analyzed_jobs)} comprehensive analysis results")
                print(f"   - Should display them automatically on page load")
                print(f"   - Should show 'Full Matching' button")
                print(f"   - Should persist across page refreshes")
                
                return True
            else:
                print("⚠️ No results returned - this would cause the frontend issue")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_shortlist_compatibility():
    """Test that shortlist and comprehensive analysis work together."""
    print("\n🧪 Testing shortlist and comprehensive analysis compatibility...")
    
    try:
        # Test shortlist
        shortlist_response = requests.get(f"{API_BASE}/api/shortlist?min_confidence=75.0")
        if shortlist_response.status_code == 200:
            shortlist_data = shortlist_response.json()
            shortlist_jobs = shortlist_data.get('shortlist', [])
            print(f"✅ Shortlist: {len(shortlist_jobs)} jobs")
        else:
            print(f"❌ Shortlist failed: {shortlist_response.status_code}")
            return False
        
        # Test comprehensive analysis
        comp_response = requests.get(f"{API_BASE}/api/jobs/comprehensive-analysis?temp=0.7&min_confidence=0.0")
        if comp_response.status_code == 200:
            comp_data = comp_response.json()
            comp_jobs = comp_data.get('analyzed_jobs', [])
            print(f"✅ Comprehensive: {len(comp_jobs)} jobs")
        else:
            print(f"❌ Comprehensive failed: {comp_response.status_code}")
            return False
        
        # Check overlap
        shortlist_ids = {job.get('id') for job in shortlist_jobs if job.get('id')}
        comp_ids = {job.get('id') for job in comp_jobs if job.get('id')}
        overlap = shortlist_ids.intersection(comp_ids)
        
        print(f"📊 Job ID overlap: {len(overlap)} jobs appear in both shortlist and comprehensive analysis")
        
        if len(overlap) > 0:
            print(f"✅ Good: There's overlap between shortlist and comprehensive analysis")
            return True
        else:
            print(f"⚠️ Warning: No overlap - might indicate data inconsistency")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run the tests."""
    print("🚀 Testing Fixed Comprehensive Analysis Loading")
    print("=" * 60)
    
    success1 = test_fixed_parameters()
    success2 = test_shortlist_compatibility()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All tests passed! The fix should work.")
        print("\n📋 Next steps:")
        print("1. Refresh the browser page")
        print("2. Check browser console for the debug messages")
        print("3. Verify comprehensive analysis results appear automatically")
    else:
        print("❌ Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
