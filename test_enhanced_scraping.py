#!/usr/bin/env python3
"""
Test the enhanced scraping that fetches full job descriptions.
"""

import os
import sys
from dotenv import load_dotenv

# Add backend to path
sys.path.append('backend')

from scraper import JobScraper

def test_enhanced_scraping():
    """Test enhanced scraping with full job description fetching."""
    load_dotenv()
    
    # Test with a URL that should have detailed job descriptions
    test_url = "https://www.hapeko.de/bewerbende/stellenangebote"
    
    print("🧪 TESTING ENHANCED SCRAPING WITH FULL DESCRIPTIONS")
    print("=" * 60)
    print(f"Testing URL: {test_url}")
    print()
    
    # Initialize scraper
    scraper = JobScraper()
    
    def log_callback(msg_type, message, data=None):
        print(f"[{msg_type.upper()}] {message}")
    
    try:
        # Test the crawl with enhanced description fetching
        jobs = scraper.crawl_url(test_url, "TEST", log_callback, force_scrape=True)
        
        print(f"\n📊 SCRAPING RESULTS:")
        print(f"   Jobs extracted: {len(jobs)}")
        print()
        
        if jobs:
            print("📋 JOB ANALYSIS:")
            
            # Analyze description quality
            salary_only_count = 0
            enhanced_count = 0
            
            for i, job in enumerate(jobs[:5], 1):  # Show first 5 jobs
                job_id = job.get('id', 'Unknown')
                title = job.get('title', 'Unknown Title')
                description = job.get('description', '')
                
                print(f"\n   Job {i} ({job_id}):")
                print(f"      Title: {title}")
                print(f"      Description Length: {len(description)} chars")
                print(f"      Description Preview: {description[:150]}...")
                
                # Check if description is just salary info
                desc_lower = description.lower()
                if len(description) < 50 and any(word in desc_lower for word in ['eur', '€', 'gehalt', '000']):
                    salary_only_count += 1
                    print(f"      Quality: ❌ SALARY ONLY")
                elif len(description) > 100:
                    enhanced_count += 1
                    print(f"      Quality: ✅ ENHANCED")
                else:
                    print(f"      Quality: ⚠️ BASIC")
            
            print(f"\n📈 DESCRIPTION QUALITY ANALYSIS:")
            print(f"   Total jobs analyzed: {min(len(jobs), 5)}")
            print(f"   Enhanced descriptions: {enhanced_count}")
            print(f"   Salary-only descriptions: {salary_only_count}")
            print(f"   Enhancement rate: {(enhanced_count / min(len(jobs), 5)) * 100:.1f}%")
            
            if enhanced_count > salary_only_count:
                print("   ✅ SUCCESS: Most jobs have enhanced descriptions!")
            else:
                print("   ⚠️ MIXED: Some jobs still have limited descriptions")
                
        else:
            print("   ❌ No jobs extracted")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

def test_comprehensive_analysis_readiness():
    """Test if the current job data is ready for comprehensive analysis."""
    print("\n" + "=" * 60)
    print("🔍 TESTING COMPREHENSIVE ANALYSIS READINESS")
    print("=" * 60)
    
    try:
        import json
        
        # Load current jobs data
        with open('data/jobs.json', 'r', encoding='utf-8') as f:
            jobs = json.load(f)
        
        print(f"📊 Current Job Data Analysis:")
        print(f"   Total jobs: {len(jobs)}")
        
        # Analyze description quality
        salary_only = 0
        short_desc = 0
        good_desc = 0
        
        for job in jobs:
            description = job.get('description', '')
            desc_lower = description.lower()
            
            if len(description) < 50 and any(word in desc_lower for word in ['eur', '€', 'gehalt', '000']):
                salary_only += 1
            elif len(description) < 100:
                short_desc += 1
            else:
                good_desc += 1
        
        print(f"\n📈 Description Quality Breakdown:")
        print(f"   Salary-only descriptions: {salary_only} ({salary_only/len(jobs)*100:.1f}%)")
        print(f"   Short descriptions: {short_desc} ({short_desc/len(jobs)*100:.1f}%)")
        print(f"   Good descriptions: {good_desc} ({good_desc/len(jobs)*100:.1f}%)")
        
        if good_desc > len(jobs) * 0.5:
            print("   ✅ READY: Most jobs have good descriptions for comprehensive analysis")
        elif good_desc > len(jobs) * 0.3:
            print("   ⚠️ PARTIAL: Some jobs ready, but could be improved")
        else:
            print("   ❌ NOT READY: Most jobs need enhanced descriptions")
            print("   💡 RECOMMENDATION: Re-scrape with enhanced description fetching")
        
        # Show examples
        print(f"\n📝 Example Descriptions:")
        
        # Show a salary-only example
        salary_example = next((job for job in jobs if len(job.get('description', '')) < 50), None)
        if salary_example:
            print(f"   Salary-only example ({salary_example.get('id')}):")
            print(f"      '{salary_example.get('description', '')}'")
        
        # Show a good example
        good_example = next((job for job in jobs if len(job.get('description', '')) > 200), None)
        if good_example:
            print(f"   Good description example ({good_example.get('id')}):")
            print(f"      '{good_example.get('description', '')[:150]}...'")
            
    except Exception as e:
        print(f"❌ ERROR analyzing current data: {str(e)}")

if __name__ == "__main__":
    # First test current data readiness
    test_comprehensive_analysis_readiness()
    
    # Then test enhanced scraping (commented out to avoid API calls during testing)
    print(f"\n💡 To test enhanced scraping, uncomment the line below:")
    print(f"   # test_enhanced_scraping()")
    
    # Uncomment to test enhanced scraping:
    # test_enhanced_scraping()
