#!/usr/bin/env python3
"""
Fix duplicate URL issues in existing job data.
Remove jobs that have duplicate URLs, keeping only the first occurrence.
"""

import json
from collections import defaultdict
import os

def fix_duplicate_urls():
    """Fix duplicate URL issues in jobs.json."""
    jobs_file = 'data/jobs.json'
    backup_file = 'data/jobs_backup_before_url_fix.json'
    
    print("🔧 FIXING DUPLICATE URL ISSUES")
    print("=" * 50)
    
    # Load existing jobs
    try:
        with open(jobs_file, 'r', encoding='utf-8') as f:
            jobs = json.load(f)
    except FileNotFoundError:
        print("❌ jobs.json file not found!")
        return
    
    print(f"📊 Original data: {len(jobs)} jobs")
    
    # Create backup
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(jobs, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup created: {backup_file}")
    
    # Track URLs and find duplicates
    url_to_jobs = defaultdict(list)
    for job in jobs:
        url = job.get('link', '')
        if url:
            url_to_jobs[url].append(job)
    
    # Find problematic URLs (those with multiple jobs)
    duplicate_urls = {url: job_list for url, job_list in url_to_jobs.items() if len(job_list) > 1}
    
    print(f"🚨 Found {len(duplicate_urls)} URLs with duplicates:")
    
    # Process each duplicate URL
    jobs_to_keep = []
    jobs_removed = 0
    
    for url, job_list in duplicate_urls.items():
        print(f"\n📎 {url}")
        print(f"   {len(job_list)} jobs found:")
        
        # Show all jobs for this URL
        for i, job in enumerate(job_list):
            print(f"   {i+1}. {job.get('id')}: {job.get('title', '')[:50]}...")
        
        # Keep only the first job (usually the most relevant one)
        job_to_keep = job_list[0]
        jobs_to_remove = job_list[1:]
        
        print(f"   ✅ Keeping: {job_to_keep.get('id')}")
        print(f"   ❌ Removing: {len(jobs_to_remove)} duplicate(s)")
        
        jobs_removed += len(jobs_to_remove)
    
    # Create cleaned job list
    seen_urls = set()
    for job in jobs:
        url = job.get('link', '')
        if url not in seen_urls:
            jobs_to_keep.append(job)
            seen_urls.add(url)
    
    print(f"\n📊 CLEANUP RESULTS:")
    print(f"   Original jobs: {len(jobs)}")
    print(f"   Jobs removed: {jobs_removed}")
    print(f"   Jobs remaining: {len(jobs_to_keep)}")
    print(f"   Unique URLs: {len(seen_urls)}")
    
    # Save cleaned data
    with open(jobs_file, 'w', encoding='utf-8') as f:
        json.dump(jobs_to_keep, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Fixed jobs.json saved!")
    print(f"   Backup available at: {backup_file}")
    
    # Verify the fix
    print(f"\n🔍 VERIFICATION:")
    url_counts = defaultdict(int)
    for job in jobs_to_keep:
        url = job.get('link', '')
        if url:
            url_counts[url] += 1
    
    duplicates_remaining = sum(1 for count in url_counts.values() if count > 1)
    if duplicates_remaining == 0:
        print("   ✅ No duplicate URLs remaining!")
    else:
        print(f"   ❌ {duplicates_remaining} duplicate URLs still exist")
    
    return len(jobs_to_keep)

def update_related_files(new_job_count):
    """Update related files that might reference job counts."""
    print(f"\n🔄 UPDATING RELATED FILES:")
    
    # Update shortlist.json if it exists
    shortlist_file = 'data/shortlist.json'
    if os.path.exists(shortlist_file):
        try:
            with open(shortlist_file, 'r', encoding='utf-8') as f:
                shortlist_data = json.load(f)
            
            # Remove shortlisted jobs that no longer exist
            valid_job_ids = set()
            with open('data/jobs.json', 'r', encoding='utf-8') as f:
                jobs = json.load(f)
                valid_job_ids = {job.get('id') for job in jobs}
            
            original_shortlist_count = len(shortlist_data)
            shortlist_data = [entry for entry in shortlist_data if entry.get('job_id') in valid_job_ids]
            
            with open(shortlist_file, 'w', encoding='utf-8') as f:
                json.dump(shortlist_data, f, indent=2, ensure_ascii=False)
            
            print(f"   📋 Updated shortlist.json: {original_shortlist_count} → {len(shortlist_data)} entries")
            
        except Exception as e:
            print(f"   ⚠️ Could not update shortlist.json: {e}")
    
    # Update comprehensive_analysis.json if it exists
    analysis_file = 'data/comprehensive_analysis.json'
    if os.path.exists(analysis_file):
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            # Remove analyses for jobs that no longer exist
            valid_job_ids = set()
            with open('data/jobs.json', 'r', encoding='utf-8') as f:
                jobs = json.load(f)
                valid_job_ids = {job.get('id') for job in jobs}
            
            original_analysis_count = len(analysis_data)
            analysis_data = [entry for entry in analysis_data if entry.get('job_id') in valid_job_ids]
            
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False)
            
            print(f"   📊 Updated comprehensive_analysis.json: {original_analysis_count} → {len(analysis_data)} entries")
            
        except Exception as e:
            print(f"   ⚠️ Could not update comprehensive_analysis.json: {e}")

if __name__ == "__main__":
    new_job_count = fix_duplicate_urls()
    if new_job_count:
        update_related_files(new_job_count)
    
    print(f"\n🎉 DUPLICATE URL FIX COMPLETE!")
    print(f"   Run 'python analyze_job_urls.py' to verify the fix.")
