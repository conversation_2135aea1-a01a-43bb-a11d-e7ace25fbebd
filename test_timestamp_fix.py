#!/usr/bin/env python3
"""
Test script to verify that the timestamp fix is working correctly.
This script will trigger a small scraping operation and check if timestamps are updated.
"""

import requests
import json
import time
from datetime import datetime

def test_timestamp_fix():
    """Test that crawled URLs get their timestamps updated even if no jobs are extracted."""
    
    print("🧪 Testing timestamp fix for crawled URLs...")
    
    # Get current source statistics
    print("\n📊 Getting current source statistics...")
    response = requests.get('http://localhost:8000/api/sources/stats')
    if response.status_code != 200:
        print(f"❌ Failed to get source stats: {response.status_code}")
        return
    
    current_stats = response.json()['source_stats']
    
    # Find a URL that has been crawled recently but shows old job dates
    hapeko_url = "https://www.hapeko.de/bewerbende/stellenangebote/p1"
    if hapeko_url in current_stats:
        hapeko_stats = current_stats[hapeko_url]
        print(f"\n🔍 Hapeko current stats:")
        print(f"   Jobs: {hapeko_stats['scraped_jobs']}")
        print(f"   Last scraped: {hapeko_stats['last_scraped']}")
    else:
        print(f"❌ Hapeko URL not found in stats")
        return
    
    # Get URL data to find the prefix
    response = requests.get('http://localhost:8000/job-urls')
    if response.status_code != 200:
        print(f"❌ Failed to get URL data: {response.status_code}")
        return
    
    url_data = response.json()
    hapeko_prefix = None
    for url_info in url_data.get('url_data', []):
        if url_info.get('url') == hapeko_url:
            hapeko_prefix = url_info.get('prefix')
            break
    
    if not hapeko_prefix:
        print(f"❌ Could not find prefix for Hapeko URL")
        return
    
    print(f"✅ Found Hapeko prefix: {hapeko_prefix}")
    
    # Trigger a scraping operation for just this URL
    print(f"\n🚀 Triggering scraping for {hapeko_prefix}...")
    scrape_data = {
        "urls": [hapeko_url],
        "force_scrape": True  # Force a new crawl to test the fix
    }
    
    response = requests.post('http://localhost:8000/api/scrape/jobs', json=scrape_data)
    if response.status_code != 200:
        print(f"❌ Failed to trigger scraping: {response.status_code}")
        print(f"Response: {response.text}")
        return
    
    scrape_result = response.json()
    print(f"✅ Scraping triggered successfully")
    print(f"   Success: {scrape_result.get('success')}")
    print(f"   Message: {scrape_result.get('message')}")
    
    # Wait a moment for the operation to complete
    print("\n⏳ Waiting for scraping to complete...")
    time.sleep(5)
    
    # Check updated source statistics
    print("\n📊 Getting updated source statistics...")
    response = requests.get('http://localhost:8000/api/sources/stats')
    if response.status_code != 200:
        print(f"❌ Failed to get updated source stats: {response.status_code}")
        return
    
    updated_stats = response.json()['source_stats']
    
    if hapeko_url in updated_stats:
        updated_hapeko_stats = updated_stats[hapeko_url]
        print(f"\n🔍 Hapeko updated stats:")
        print(f"   Jobs: {updated_hapeko_stats['scraped_jobs']}")
        print(f"   Last scraped: {updated_hapeko_stats['last_scraped']}")
        
        # Compare timestamps
        old_timestamp = hapeko_stats['last_scraped']
        new_timestamp = updated_hapeko_stats['last_scraped']
        
        if new_timestamp != old_timestamp:
            print(f"\n✅ SUCCESS: Timestamp was updated!")
            print(f"   Old: {old_timestamp}")
            print(f"   New: {new_timestamp}")
        else:
            print(f"\n❌ FAILED: Timestamp was not updated")
            print(f"   Timestamp: {old_timestamp}")
    else:
        print(f"❌ Hapeko URL not found in updated stats")

if __name__ == "__main__":
    test_timestamp_fix()
