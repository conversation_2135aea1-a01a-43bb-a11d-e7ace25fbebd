#!/usr/bin/env python3
"""
Fix source display names to show domain names instead of generic "Job Source XXX".
"""

import json
import os
from urllib.parse import urlparse

def extract_domain_name(url):
    """Extract domain name from URL for display purposes."""
    try:
        parsed_url = urlparse(url)
        domain_parts = parsed_url.hostname.split('.')
        if len(domain_parts) >= 2:
            # Get the main domain name (second-to-last part)
            # e.g., "hapeko" from "www.hapeko.de"
            return domain_parts[-2]
        else:
            return parsed_url.hostname or 'unknown'
    except Exception:
        return 'unknown'

def fix_job_urls():
    """Fix job_urls.json to use domain names instead of generic names."""
    job_urls_file = 'data/job_urls.json'
    backup_file = 'data/job_urls_backup_before_domain_fix.json'
    
    print("🔧 FIXING JOB URL DISPLAY NAMES")
    print("=" * 50)
    
    # Load existing job URLs
    try:
        with open(job_urls_file, 'r', encoding='utf-8') as f:
            job_urls = json.load(f)
    except FileNotFoundError:
        print("❌ job_urls.json file not found!")
        return
    
    print(f"📊 Found {len(job_urls)} job URLs")
    
    # Create backup
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(job_urls, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup created: {backup_file}")
    
    # Fix display names
    updated_count = 0
    
    for url_data in job_urls:
        url = url_data.get('url', '')
        current_name = url_data.get('name', '')
        prefix = url_data.get('prefix', '')
        
        # Check if name is generic "Job Source XXX" format
        if current_name.startswith('Job Source ') or current_name == prefix:
            domain_name = extract_domain_name(url)
            url_data['name'] = domain_name
            updated_count += 1
            print(f"   Updated {prefix}: '{current_name}' → '{domain_name}'")
    
    # Save updated data
    with open(job_urls_file, 'w', encoding='utf-8') as f:
        json.dump(job_urls, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Updated {updated_count} job URL names")
    print(f"💾 Updated job_urls.json saved")
    
    return updated_count

def test_domain_extraction():
    """Test domain extraction on various URLs."""
    print("\n" + "=" * 50)
    print("🧪 TESTING DOMAIN EXTRACTION")
    print("=" * 50)
    
    test_urls = [
        "https://www.hapeko.de/bewerbende/stellenangebote",
        "https://vakanzen.drmaier-partner.de/stellenanzeigen/",
        "https://www.baumann-ag.com/fuer-kandidaten/bewerbungsverfahren/vakanzen/joblist",
        "https://www.mercuriurval.com/de-de/chancen/?filter=country-de",
        "https://www.meyheadhunter.de/mandate/",
        "https://www.psp.de/stellenausschreibungen.html",
        "https://jobs.example.com/listings",
        "http://subdomain.company.co.uk/jobs"
    ]
    
    print("📋 Testing domain extraction:")
    for url in test_urls:
        domain = extract_domain_name(url)
        print(f"   {url}")
        print(f"   → {domain}")
        print()

def verify_fix():
    """Verify that the fix worked by checking current job URLs."""
    print("\n" + "=" * 50)
    print("🔍 VERIFYING FIX")
    print("=" * 50)
    
    job_urls_file = 'data/job_urls.json'
    
    try:
        with open(job_urls_file, 'r', encoding='utf-8') as f:
            job_urls = json.load(f)
        
        print(f"📊 Current job URLs:")
        generic_count = 0
        domain_count = 0
        
        for url_data in job_urls:
            prefix = url_data.get('prefix', '')
            name = url_data.get('name', '')
            url = url_data.get('url', '')
            
            if name.startswith('Job Source ') or name == prefix:
                generic_count += 1
                status = "❌ GENERIC"
            else:
                domain_count += 1
                status = "✅ DOMAIN"
            
            print(f"   {prefix}: {name} ({status})")
            print(f"      URL: {url[:60]}{'...' if len(url) > 60 else ''}")
        
        print(f"\n📈 RESULTS:")
        print(f"   Domain names: {domain_count}")
        print(f"   Generic names: {generic_count}")
        
        if generic_count == 0:
            print("   ✅ SUCCESS: All URLs have domain names!")
        else:
            print("   ⚠️ Some URLs still have generic names")
            
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")

def main():
    """Run the source display name fix."""
    print("🔧 SOURCE DISPLAY NAME FIX")
    print("=" * 60)
    
    # Test domain extraction first
    test_domain_extraction()
    
    # Fix job URLs
    updated_count = fix_job_urls()
    
    # Verify the fix
    verify_fix()
    
    print("\n" + "=" * 60)
    print("🎉 SOURCE DISPLAY FIX COMPLETE!")
    
    if updated_count > 0:
        print(f"\n💡 NEXT STEPS:")
        print("   1. Restart the backend server to pick up changes")
        print("   2. Test shortlisting to see domain names like 'hapeko' instead of 'Job Source AAD'")
        print("   3. Check comprehensive analysis results")
    else:
        print("\n💡 No updates needed - all URLs already have domain names")

if __name__ == "__main__":
    main()
