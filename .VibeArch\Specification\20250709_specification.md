# JoMaDe_Dev Specification

JoMaDe Application Architecture & Specification Analysis
1. Overall Application Architecture and Purpose
JoMaDe (Job Market Detector) is a web-based job market analysis and matching application. The application helps users find relevant job postings based on their CV and skills and submitted job-related urls from headhunters and companies through automated scraping, AI-powered shortlisting comparing a cv summary to a job title, subsequently a comprehensive analysis comparing the cv to the job description.

Architecture Overview:
Backend: FastAPI-based REST API with minimal dependencies
Frontend: Simplified single-page HTML application
Data Storage: Hybrid JSON file-based storage with SQLite for complex operations
External Integrations: Firecrawl API for web scraping, OpenAI for job matching
2. Key Functional Requirements and Features
Core Features:
Job URL Management: Add, edit, and manage job source URLs with unique prefixes
Web Scraping: Automated job scraping from multiple sources using Firecrawl
CV Management: Upload, edit, and maintain CV summaries
Job Matching: AI-powered job matching based on CV content
Shortlisting: Intelligent job shortlisting with confidence scores
Real-time Logging: Live feedback during scraping operations
Document Management: File upload and embedding capabilities for RAG-based search
Advanced Features:
Date-based caching to prevent re-scraping
Statistics per source URL
Title-based deduplication
CV-based job title matching
Fail-hard loading behavior (no mock data generation)
3. Data Flow and Processing Logic
Job Processing Workflow:
URL Configuration → Users configure job source URLs with unique prefixes (AAA, AAB, etc.)
Scraping Initiation → System crawls configured URLs using Firecrawl API
Content Extraction → OpenAI processes scraped content to extract structured job data
Data Storage → Jobs stored with unique IDs, source tracking, and metadata
CV Matching → AI compares job descriptions against user's CV summary
Shortlisting → High-scoring matches added to shortlist with confidence percentages
User Review → Users review shortlisted jobs and make decisions
Data Processing Features:
Timeout Management: 5-minute timeout per URL to prevent hanging
Error Handling: Comprehensive logging and fail-fast behavior
Deduplication: Title-based duplicate detection
Source Tracking: Each job linked to its source URL with prefix system
4. User Interface Components and Interactions
Frontend Architecture (Simplified HTML):
Single Page Application: frontend/index.html with embedded JavaScript
Real-time Updates: WebSocket-like streaming for live scraping feedback
Responsive Design: Mobile-friendly interface with modern styling
Component Areas:
System status dashboard
Job URL management interface
CV summary editor
Job scraping controls
Job listings with filtering
Shortlist management
Key UI Features:
Status Indicators: Real-time backend health monitoring
Progress Tracking: Live scraping progress with detailed logging
Interactive Tables: Sortable job listings with action buttons
Form Management: Dynamic URL addition/removal
Visual Feedback: Color-coded status indicators and animations
5. Backend Services and API Endpoints
Core API Structure (backend/api.py):
backend
API Endpoint Categories:
Job URLs: CRUD operations for source URL management
CV Management: Get/update CV summaries
Job Operations: Scraping, listing, shortlisting
Real-time Logging: Streaming endpoints for live updates
Statistics: Source-specific job statistics
Health Checks: System status monitoring
Key Services:
Scraping Service: Firecrawl integration with timeout handling
Matching Service: OpenAI-powered CV-job matching
Storage Service: JSON file-based data persistence
Logging Service: Real-time operation feedback
6. Database Schema and Data Models
Data Storage Strategy (Hybrid Approach):
backend
Data Models:
Job URLs (data/job_urls.json):
Jobs (data/jobs.json):
Shortlist (data/shortlist.json):
7. Integration Points with External Services
Primary Integrations:
Firecrawl API: Web scraping service for job content extraction
OpenAI API: AI-powered job description processing and CV matching
Environment Variables: Secure API key management via .env files
Integration Features:
Timeout Handling: 5-minute per-URL timeout to prevent API hanging
Error Recovery: Graceful failure handling with detailed logging
Rate Limiting: Respectful API usage patterns
Authentication: Secure API key validation on startup
8. Current Implementation Status and Migration Details
Migration Success (Complete Simplification):
✅ Frontend Complexity Reduced: From 453 npm packages to 1 package
✅ Startup Time: From 5+ minutes to 5 seconds
✅ Build Process: Eliminated complex TypeScript compilation
✅ Data Migration: Successfully migrated from Markdown to JSON storage
✅ Core Functionality: All basic features operational
Current Status:
Backend: Fully functional FastAPI with 6 dependencies
Frontend: Simplified HTML with real-time backend integration
Data Storage: JSON-based persistence with automatic migration
Scraping: Firecrawl integration with comprehensive error handling
Matching: OpenAI-powered CV-job matching with confidence scoring
Technical Debt Resolved:
✅ Consolidated run scripts
✅ Standardized environment variables
✅ Implemented comprehensive error handling
✅ Established hybrid storage approach
✅ Simplified frontend architecture
Next Development Phases:
Phase 1: Enhanced testing infrastructure and authentication
Phase 2: Advanced UI/UX improvements and performance optimization
Phase 3: Mobile applications and premium features
The application successfully demonstrates a "simplicity-first" approach, achieving high functionality with minimal complexity while maintaining professional-grade features for job market analysis and matching.