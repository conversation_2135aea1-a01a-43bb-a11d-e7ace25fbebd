# 🎯 Jomade MVP - Executive Search Application

A comprehensive executive search and job management platform with advanced analytics, AI-powered matching, and intelligent URL management.

## 🚀 Quick Start

### Option 1: Using the Launcher Scripts (Recommended)

1. **Double-click** `start_jomade.bat` to start the application
2. The application will automatically:
   - Check Python installation
   - Install/verify dependencies
   - Start the backend server
   - Display the URL: http://localhost:3000

### Option 2: Create Desktop Shortcuts

1. **Right-click** on `create_shortcut.ps1` and select "Run with PowerShell"
2. This creates desktop and start menu shortcuts
3. **Right-click** the desktop shortcut and select "Pin to taskbar"
4. Now you can start Jomade MVP directly from your taskbar!

### Option 3: Manual Start

```bash
# Install dependencies
pip install -r requirements.txt

# Start the application
python backend/app.py
```

## 📋 System Requirements

- **Python 3.8+** (with pip)
- **Windows 10/11** (for taskbar integration)
- **Modern web browser** (Chrome, Firefox, Edge)
- **Internet connection** (for AI features and web scraping)

## 🌟 Key Features

### 📊 Advanced Analytics
- **2D Cluster Visualization** - See job similarities in interactive plots
- **Category & Seniority Analysis** - Understand job distribution patterns
- **Real-time Statistics** - Track scraping performance and job counts

### 🎯 Smart URL Management
- **Executive Search Priority** - Focus on high-value executive positions
- **Contact-Only URLs** - Maintain non-scrapable firms for email campaigns
- **Advanced Filtering** - Filter by category, job level, region, status
- **Bulk Operations** - Enable/disable scraping for multiple URLs

### 🤖 AI-Powered Features
- **CV Matching** - AI-powered job-to-CV compatibility scoring
- **Smart Categorization** - Automatic job classification and tagging
- **Intelligent Shortlisting** - AI-assisted candidate-job matching

### Option 2: Manual setup

#### Backend Setup

1. Navigate to the backend directory:
   ```
   cd JoMaDe/backend
   ```

2. Create a virtual environment:
   ```
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows Command Prompt:
     ```
     .venv\Scripts\activate
     ```
   - Windows PowerShell:
     ```
     .venv\Scripts\Activate.ps1
     ```

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

#### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd JoMaDe/frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

#### Running the Application Manually

1. Start the backend server:
   ```
   cd backend
   .venv\Scripts\python -m uvicorn api:app --reload --host 0.0.0.0 --port 8000
   ```

2. In a separate terminal, start the frontend server:
   ```
   cd frontend
   npm run dev
   ```

4. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - API Documentation: http://localhost:8000/docs

## Project Structure

- `backend/`: FastAPI backend application
  - `api/`: API endpoints
  - `core/`: Core functionality
  - `models/`: Database models
  - `services/`: Business logic
  - `utils/`: Utility functions

- `frontend/`: Simple HTML frontend application
  - `index.html`: Main application page
  - `settings.html`: Settings page
  - `404.html`: Error page
  - `package.json`: Minimal dependencies (http-server)

- `data/`: Application data storage
  - `job_urls.json`: Job source URLs
  - `cv.json`: CV summary data
  - `jobs.json`: Scraped job data
  - `shortlist.json`: Shortlisted jobs

## Configuration

- Environment variables are stored in `.env` (create from `.env.example`)
- Required API keys: FIRECRAWL_API_KEY, OPENAI_API_KEY
- Backend uses JSON file storage in `data/` directory

## Development

- Backend API documentation is available at http://localhost:8000/docs
- Frontend application is available at http://localhost:3000

## API Endpoints

### Core Endpoints

- `GET /`: Root endpoint with API information
- `GET /health`: Health check with system status

### Job URLs Management

- `GET /api/urls`: Get all job URLs
- `POST /api/urls`: Add a new job URL
- `PUT /api/urls/{url_id}`: Update a job URL
- `DELETE /api/urls/{url_id}`: Delete a job URL

### CV Management

- `GET /api/cv`: Get CV summary
- `POST /api/cv`: Update CV summary

### Job Operations

- `GET /api/jobs`: Get all jobs
- `POST /api/scrape`: Trigger job scraping
- `POST /api/shortlist`: Create job shortlist based on CV match
- `GET /api/shortlist`: Get shortlisted jobs

### Real-time Features

- `GET /api/scrape/logs`: Stream real-time scraping logs
- `GET /api/sources/stats`: Get statistics by source

## Workflow

1. Configure job source URLs in the application
2. Add or edit your CV summary
3. Trigger job scraping from configured sources
4. Review scraped jobs and statistics
5. Create shortlist based on CV matching
6. Review and evaluate shortlisted jobs

## Features

- **Real-time Scraping**: Live feedback during job scraping operations
- **AI-Powered Matching**: OpenAI-based CV-job matching with confidence scores
- **Source Management**: Easy addition and management of job source URLs
- **Date-based Caching**: Prevents re-scraping on the same day
- **Statistics Tracking**: Per-source job statistics and scraping history