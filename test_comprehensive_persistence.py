#!/usr/bin/env python3
"""
Test script to verify comprehensive analysis data persistence functionality.
Tests both the backend API endpoint and the expected behavior.
"""

import requests
import json
import sys

API_BASE = "http://localhost:8000"

def test_comprehensive_analysis_endpoint():
    """Test the new GET endpoint for comprehensive analysis results."""
    print("🧪 Testing comprehensive analysis persistence...")
    
    try:
        # Test the new GET endpoint
        response = requests.get(f"{API_BASE}/api/jobs/comprehensive-analysis?temp=0.7&min_confidence=0.0")
        
        if response.status_code != 200:
            print(f"❌ GET endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        
        if not data.get('success'):
            print(f"❌ API returned success=false: {data.get('message')}")
            return False
        
        analyzed_jobs = data.get('analyzed_jobs', [])
        print(f"✅ GET endpoint working: Retrieved {len(analyzed_jobs)} comprehensive analysis results")
        
        if analyzed_jobs:
            # Check the structure of the first job
            first_job = analyzed_jobs[0]
            required_fields = ['stage1_confidence', 'stage2_confidence', 'confidence_change', 'pros', 'cons', 'comprehensive_analysis']
            
            missing_fields = [field for field in required_fields if field not in first_job]
            if missing_fields:
                print(f"⚠️ Missing fields in job data: {missing_fields}")
            else:
                print("✅ Job data structure is correct")
                
            # Show sample data
            print(f"📊 Sample job: {first_job.get('title', 'No title')}")
            print(f"   Stage 1 Confidence: {first_job.get('stage1_confidence')}%")
            print(f"   Stage 2 Confidence: {first_job.get('stage2_confidence')}%")
            print(f"   Confidence Change: {first_job.get('confidence_change')}%")
            print(f"   Pros: {len(first_job.get('pros', []))} items")
            print(f"   Cons: {len(first_job.get('cons', []))} items")
            print(f"   From Cache: {first_job.get('from_cache', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def test_cv_status_endpoint():
    """Test CV status endpoint to understand shortlist state."""
    print("\n🧪 Testing CV status endpoint...")
    
    try:
        response = requests.get(f"{API_BASE}/api/shortlist/cv-status")
        
        if response.status_code != 200:
            print(f"❌ CV status endpoint failed: {response.status_code}")
            return False
        
        data = response.json()
        status = data.get('status', {})
        
        print(f"✅ CV Status:")
        print(f"   Has Shortlist: {status.get('has_shortlist', False)}")
        print(f"   CV Changed: {status.get('cv_changed', False)}")
        print(f"   Needs Refresh: {status.get('needs_refresh', False)}")
        print(f"   Active Entries: {status.get('active_entries', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing CV status: {e}")
        return False

def test_shortlist_endpoint():
    """Test shortlist endpoint to see existing shortlisted jobs."""
    print("\n🧪 Testing shortlist endpoint...")
    
    try:
        response = requests.get(f"{API_BASE}/api/shortlist?min_confidence=0.0")
        
        if response.status_code != 200:
            print(f"❌ Shortlist endpoint failed: {response.status_code}")
            return False
        
        data = response.json()
        shortlist = data.get('shortlist', [])
        
        print(f"✅ Shortlist: {len(shortlist)} jobs")
        
        if shortlist:
            first_job = shortlist[0]
            print(f"📊 Sample shortlisted job: {first_job.get('title', 'No title')}")
            print(f"   Confidence: {first_job.get('confidence_percentage', 0)}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing shortlist: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing JoMaDe Comprehensive Analysis Data Persistence")
    print("=" * 60)
    
    # Test backend health
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False
    
    # Run tests
    tests = [
        test_cv_status_endpoint,
        test_shortlist_endpoint,
        test_comprehensive_analysis_endpoint
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 60)
    if all(results):
        print("🎉 All tests passed! Comprehensive analysis persistence is working.")
        print("\n📋 Expected behavior:")
        print("   1. Page loads shortlisted jobs from cache")
        print("   2. Page loads comprehensive analysis results from cache")
        print("   3. Both are displayed simultaneously")
        print("   4. Full Matching button is visible")
        print("   5. Results persist across page refreshes")
    else:
        print("❌ Some tests failed. Check the output above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
