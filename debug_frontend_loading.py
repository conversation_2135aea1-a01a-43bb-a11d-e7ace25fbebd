#!/usr/bin/env python3
"""
Debug script to test the frontend loading behavior and identify issues.
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_comprehensive_analysis_with_different_params():
    """Test the comprehensive analysis endpoint with different parameters."""
    print("🔍 Testing comprehensive analysis endpoint with different parameters...")
    
    test_cases = [
        {"temp": 0.7, "min_confidence": 0.0, "description": "Default frontend params"},
        {"temp": 0.7, "min_confidence": 75.0, "description": "UI default min_confidence"},
        {"temp": 0.7, "min_confidence": 50.0, "description": "Lower confidence threshold"},
    ]
    
    for case in test_cases:
        try:
            url = f"{API_BASE}/api/jobs/comprehensive-analysis?temp={case['temp']}&min_confidence={case['min_confidence']}"
            print(f"\n📡 Testing: {case['description']}")
            print(f"   URL: {url}")
            
            response = requests.get(url)
            
            if response.status_code == 200:
                data = response.json()
                analyzed_jobs = data.get('analyzed_jobs', [])
                print(f"   ✅ Success: {len(analyzed_jobs)} jobs returned")
                
                if analyzed_jobs:
                    # Show confidence range
                    confidences = [job.get('stage2_confidence', 0) for job in analyzed_jobs]
                    print(f"   📊 Confidence range: {min(confidences):.1f}% - {max(confidences):.1f}%")
                else:
                    print("   ⚠️ No jobs returned - check confidence threshold")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_shortlist_status():
    """Test shortlist status to understand the current state."""
    print("\n🔍 Testing shortlist status...")
    
    try:
        response = requests.get(f"{API_BASE}/api/shortlist/cv-status")
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', {})
            print(f"   ✅ CV Status:")
            print(f"      Has Shortlist: {status.get('has_shortlist')}")
            print(f"      CV Changed: {status.get('cv_changed')}")
            print(f"      Needs Refresh: {status.get('needs_refresh')}")
            print(f"      Active Entries: {status.get('active_entries')}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_shortlist_endpoint():
    """Test shortlist endpoint with different confidence levels."""
    print("\n🔍 Testing shortlist endpoint...")
    
    test_cases = [0.0, 75.0, 50.0]
    
    for min_conf in test_cases:
        try:
            url = f"{API_BASE}/api/shortlist?min_confidence={min_conf}"
            print(f"\n📡 Testing shortlist with min_confidence={min_conf}")
            
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                shortlist = data.get('shortlist', [])
                print(f"   ✅ Success: {len(shortlist)} shortlisted jobs")
                
                if shortlist:
                    confidences = [job.get('confidence_percentage', 0) for job in shortlist]
                    print(f"   📊 Confidence range: {min(confidences):.1f}% - {max(confidences):.1f}%")
            else:
                print(f"   ❌ Failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def main():
    """Run debugging tests."""
    print("🚀 Debugging JoMaDe Frontend Loading Issues")
    print("=" * 60)
    
    test_shortlist_status()
    test_shortlist_endpoint()
    test_comprehensive_analysis_with_different_params()
    
    print("\n" + "=" * 60)
    print("🔍 Analysis:")
    print("1. Check if comprehensive analysis returns results with min_confidence=0.0")
    print("2. Verify if shortlist and comprehensive analysis have matching confidence levels")
    print("3. Ensure frontend is using correct parameters")
    print("\n💡 Potential Issues:")
    print("- Frontend might be using wrong min_confidence value")
    print("- Timing issue with DOM element availability")
    print("- API call might be failing silently")
    print("- displayComprehensiveAnalysis() function might have issues")

if __name__ == "__main__":
    main()
