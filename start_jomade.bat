@echo off
REM Jomade MVP Application Launcher
REM This script starts the Jomade MVP application

title Jomade MVP - Executive Search Application

echo ========================================
echo    Starting Jomade MVP Application
echo ========================================
echo.

REM Set the application directory
set APP_DIR=%~dp0
cd /d "%APP_DIR%"

echo Application Directory: %APP_DIR%
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your system PATH
    echo.
    pause
    exit /b 1
)

echo Python is available:
python --version
echo.

REM Check if required files exist
if not exist "backend\app.py" (
    echo ERROR: backend\app.py not found
    echo Please ensure you're running this from the Jomade MVP root directory
    echo.
    pause
    exit /b 1
)

if not exist "frontend\index.html" (
    echo ERROR: frontend\index.html not found
    echo Please ensure you're running this from the Jomade MVP root directory
    echo.
    pause
    exit /b 1
)

echo Required files found
echo.

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
    echo Virtual environment activated
) else (
    echo No virtual environment found, using system Python
)
echo.

REM Install/check dependencies
echo Checking dependencies...
pip install -r requirements.txt --quiet
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Please check your requirements.txt file
    echo.
    pause
    exit /b 1
)
echo Dependencies checked
echo.

REM Create data directory if it doesn't exist
if not exist "data" (
    echo Creating data directory...
    mkdir data
)

REM Start the application
echo Starting Jomade MVP Backend Server...
echo.
echo The application will be available at:
echo    http://localhost:3000
echo.
echo To stop the application, press Ctrl+C in this window
echo.
echo ========================================
echo.

REM Start the Python backend
python backend\app.py

REM If we get here, the application has stopped
echo.
echo ========================================
echo Jomade MVP Application has stopped
echo ========================================
echo.
pause
