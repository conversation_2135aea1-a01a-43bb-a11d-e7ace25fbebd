#!/usr/bin/env python3
"""
Fix existing shortlist entries to include total_job_count_when_created field.
"""

import json
import os

def fix_shortlist():
    """Add total_job_count_when_created field to existing shortlist entries."""
    
    # Path to shortlist file
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    shortlist_file = os.path.join(data_dir, "shortlist.json")
    
    if not os.path.exists(shortlist_file):
        print("No shortlist file found.")
        return
    
    # Load existing data
    with open(shortlist_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Found {len(data)} shortlist entries")
    
    # Update each entry to include total_job_count_when_created
    updated_count = 0
    for entry in data:
        if 'total_job_count_when_created' not in entry:
            # Use 159 as the default since that's the current job count
            entry['total_job_count_when_created'] = 159
            updated_count += 1
            print(f"Updated entry {entry.get('job_id', 'unknown')}")
    
    # Save updated data
    with open(shortlist_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"Updated {updated_count} entries with total_job_count_when_created field")
    print("Shortlist file fixed successfully!")

if __name__ == "__main__":
    fix_shortlist()
